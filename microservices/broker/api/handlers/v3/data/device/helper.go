package device

import (
	"fmt"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
)

// Convert a slice of pgDeviceInfo to a slice of dataPayload.
func convertPgDeviceInfos(dbInfo *[]pgDeviceInfo) *[]dataPayload {
	// If the input pointer is nil, return nil
	if dbInfo == nil {
		return nil
	}
	infos := *dbInfo
	out := make([]dataPayload, 0, len(infos))
	for _, d := range infos {
		// Default to either error or nevercomm, redis will override "error" if the gateway is sending data for the device
		state := "error"
		if !d.IsEnabled {
			state = "nevercomm"
		}
		dp := dataPayload{
			DeviceID:         d.ID,
			DeviceIdentifier: d.DeviceID,
			Location: location{
				Latitude:  d.Latitude,
				Longitude: d.Longitude,
			},
			Status: deviceStatus{
				State:                state,
				HeartbeatReceivedUTC: "", // not available
				LogUploadedUTC:       d.DateUploadedUTC.Format(time.RFC3339Nano),
				LastFaultReason:      d.Fault,
				LastFaultUploadedUTC: d.MonitorTime.Format(time.RFC3339Nano),
				FaultedChannelStatus: channelStatus{
					ChannelRed:    d.ChannelRedStatus,
					ChannelYellow: d.ChannelYellowStatus,
					ChannelGreen:  d.ChannelGreenStatus,
				},
			},
			Metadata: deviceMetadata{
				// TODO: Need to create a process to figure this out.
				Manufacturer:            "EDI", // not available, Setting a default for now.
				Model:                   "",    // not available
				UserAssignedDeviceID:    fmt.Sprint(d.MonitorID),
				UserAssignedDeviceName:  d.MonitorName,
				ApplicationVersion:      "", // not available
				FirmwareType:            "", // not available
				FirmwareVersion:         "", // not available
				CommVersion:             "", // not available
				RmsEngineFirmwareType:   d.EngineVersion,
				RmsEngineFirmwareVerson: d.EngineRevision,
				IPAddress:               d.IPAddress,
				IPort:                   d.Port,
			},
		}
		out = append(out, dp)
	}
	return &out
}

func addRedisToPayload(dp *dataPayload, header *helper.HeaderRecord, status *helper.RmsStatusRecord) {
	dp.Metadata.Model = status.DeviceModel
	dp.Metadata.FirmwareType = header.FirmwareVersion
	dp.Metadata.FirmwareVersion = header.FirmwareRevision
	dp.Metadata.CommVersion = header.CommVersion

	dp.Status.HeartbeatReceivedUTC = status.MonitorTime.UTC().Format(time.RFC3339Nano)
	if status.IsFaulted {
		dp.Status.State = "faulted"
	} else {
		dp.Status.State = "nofault"
	}
}
