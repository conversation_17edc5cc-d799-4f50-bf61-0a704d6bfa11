// Package authenticate encapsulates the logic dealing with user access requests
package authenticate

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"time"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	response "synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// Type definitions for dependencies
type (
	ConnectionProvider func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	JWTCreator         func(userName string, duration time.Duration, permissions jwttokens.UserPermissions) (string, time.Time, error)
	TokenPersister     func(pg connect.DatabaseExecutor, userID string, jwt string, expiresAt time.Time) error
)

// PasswordHasher hashes passwords
type PasswordHasher func(password string) string

// UserPermissionsCreator creates user permissions
type UserPermissionsCreator func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error)

// TimeProvider provides the current time
type TimeProvider func() time.Time

// NonceGenerator generates a nonce
type NonceGenerator func(length int) ([]byte, error)

// UserHandler handles user authentication
type UserHandler struct {
	DBProvider             ConnectionProvider
	PasswordHasher         PasswordHasher
	JwtCreator             JWTCreator
	TokenPersister         TokenPersister
	UserPermissionsCreator UserPermissionsCreator
	TimeProvider           TimeProvider
	NonceGenerator         NonceGenerator
}

// UserAccess takes a username and password and returns a payload which includes a jwt to the caller
func (h *UserHandler) UserAccess(userName, password string, w http.ResponseWriter, r *http.Request) {
	// Create hash of the password - all passwords are stored hashed in the db
	hashedPassword := h.PasswordHasher(password)

	// Get the postgres connection
	connections, err := h.DBProvider(r.Context())
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("%v", err) // Database connection errors are ERROR level
		return
	}

	pg := connections.Postgres

	// Lookup the username in the db
	// We are returning the user id as the api key because we are not using the api key for anything
	// but we still need to return something
	query := `
		SELECT
			u.Id,
			u.OrigID,
			744 as TokenDurationHours,
			m.OrganizationId as OrganizationIdentifier,
			u.Id as APIKey
		FROM {{User}} u
		LEFT JOIN {{AuthMethod}} am
			ON am.UserId = u.Id
		LEFT JOIN {{Memberships}} m
			ON m.AuthMethodId = am.Id
		WHERE am.username ILIKE $1 AND am.PasswordHash = $2 AND am.IsEnabled
		LIMIT 1
		`

	// Use QueryRowStruct with the dbUser struct
	user := dbUser{}
	err = pg.QueryRowStruct(&user, query, userName, hashedPassword)

	switch {
	case errors.Is(err, sql.ErrNoRows):
		// Username and password combo not found -- update the failed attempts for the user name and return not-authorized
		// Could be the username is bogus -- that's fine - no records will be updated
		query = `
			UPDATE
				{{AuthMethod}}
			SET FailedLoginAttempts = FailedLoginAttempts + 1,
				IsEnabled =
					CASE
						WHEN FailedLoginAttempts >= 3 THEN FALSE
						ELSE IsEnabled
					END
			WHERE UserName = $1`
		_, err = pg.Exec(query, userName)

		logger.Warnf("%v: username: %s, err: %v", ErrInvalidCredentials, userName, err) // Authentication failures are WARN level
		response.CreateUnauthorizedResponse(w)
		return
	case err != nil:
		logger.Errorf("%v: %v", ErrDatabaseQuery, err) // Database query errors are ERROR level
		response.CreateUnauthorizedResponse(w)
		return
	default:
		// Use the struct fields directly instead of GetColumn calls
		userID := user.Orig_ID
		tokenExpirationInHours := user.TokenDurationHours
		organizationID := user.OrganizationIdentifier
		apiKey := user.APIKey

		roleName := "deprecated"

		// Reset failed login attempts and update last login time
		query = "UPDATE {{AuthMethod}} SET FailedLoginAttempts = 0, LastLogin = $1 WHERE UserId = $2"
		_, err = pg.Exec(query, h.TimeProvider().UTC(), user.Id)
		if err != nil {
			logger.Warnf("%v: user %s, err: %v", ErrLoginTimeUpdate, userName, err) // Non-critical errors are WARN level
			// Continue despite this error - it's not critical
		}

		// Create user detail record
		userDetail := &userDetailRecord{}
		userDetail.UserID = userID
		userDetail.OrganizationID = organizationID
		userDetail.Username = userName
		userDetail.APIKey = apiKey
		userDetail.Role = roleName

		// Get user permissions
		var userPermissions *jwttokens.UserPermissions
		if userPermissions, err = h.UserPermissionsCreator(pg, user.Id); err != nil {
			logger.Errorf("%v: user ID %d, err: %v", ErrUserPermissions, userID, err) // User permissions errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Create JWT token
		duration := time.Duration(tokenExpirationInHours) * time.Hour
		jwt, expiresAt, err := h.JwtCreator(userName, duration, *userPermissions)
		if err != nil {
			logger.Errorf("%v: %v", ErrJWTCreation, err) // JWT creation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Persist token to database
		err = h.TokenPersister(pg, user.Id, jwt, expiresAt)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenPersistence, err) // Token persistence errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Create and send response
		jwtMsg := &dataUserResponsePayload{}
		jwtMsg.User = *userDetail
		jwtMsg.Token = jwt
		response.CreateAuthSuccessResponse(jwtMsg, w)
		return
	}
}

// NewUserHandler creates a new UserHandler with default dependencies
func NewUserHandler() *UserHandler {
	return &UserHandler{
		DBProvider:             connect.GetConnections,
		PasswordHasher:         security.CalculateSHA256,
		JwtCreator:             jwttokens.CreateJwtTokenUsingDuration,
		TokenPersister:         persistTokenToDB,
		UserPermissionsCreator: createUserPermissions,
		TimeProvider:           time.Now,
		NonceGenerator:         generateNonce,
	}
}
