package user

import (
	"net/http"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	RestUserPermissions "synapse-its.com/onramp/modules/user/permissions"

	"github.com/gorilla/mux"
)

type Handler struct {
	sessionStore domain.SessionStore
}

func NewHandler(sessionStore domain.SessionStore) *Handler {
	return &Handler{
		sessionStore: sessionStore,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Apply session middleware to user routes
	userRouter := router.PathPrefix("/user").Subrouter()
	userRouter.Use(middlewares.SessionMiddleware(h.sessionStore))

	userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)
}
